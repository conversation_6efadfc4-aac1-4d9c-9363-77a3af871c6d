# SMTP Configuration Examples for InvenioRDM

## Configuration Location
Add these settings to your `my-site/invenio.cfg` file in the Email Configuration section.

## Common SMTP Providers

### 1. Gmail Configuration
```python
# Gmail SMTP Settings
MAIL_SERVER = 'smtp.gmail.com'
MAIL_PORT = 587
MAIL_USE_TLS = True
MAIL_USE_SSL = False
MAIL_USERNAME = '<EMAIL>'
MAIL_PASSWORD = 'your-app-password'  # Use App Password, not regular password
MAIL_DEFAULT_SENDER = '<EMAIL>'
SECURITY_EMAIL_SENDER = '<EMAIL>'
```

**Gmail Setup Steps:**
1. Enable 2-Factor Authentication on your Google account
2. Generate an App Password: Google Account → Security → App passwords
3. Use the App Password (not your regular password)

### 2. Outlook/Hotmail Configuration
```python
# Outlook SMTP Settings
MAIL_SERVER = 'smtp-mail.outlook.com'
MAIL_PORT = 587
MAIL_USE_TLS = True
MAIL_USE_SSL = False
MAIL_USERNAME = '<EMAIL>'
MAIL_PASSWORD = 'your-password'
MAIL_DEFAULT_SENDER = '<EMAIL>'
SECURITY_EMAIL_SENDER = '<EMAIL>'
```

### 3. Yahoo Mail Configuration
```python
# Yahoo SMTP Settings
MAIL_SERVER = 'smtp.mail.yahoo.com'
MAIL_PORT = 587
MAIL_USE_TLS = True
MAIL_USE_SSL = False
MAIL_USERNAME = '<EMAIL>'
MAIL_PASSWORD = 'your-app-password'  # Use App Password
MAIL_DEFAULT_SENDER = '<EMAIL>'
SECURITY_EMAIL_SENDER = '<EMAIL>'
```

### 4. Custom SMTP Server
```python
# Custom SMTP Settings
MAIL_SERVER = 'mail.yourdomain.com'
MAIL_PORT = 587  # or 25, 465 depending on your server
MAIL_USE_TLS = True  # or False
MAIL_USE_SSL = False  # or True for port 465
MAIL_USERNAME = '<EMAIL>'
MAIL_PASSWORD = 'your-password'
MAIL_DEFAULT_SENDER = '<EMAIL>'
SECURITY_EMAIL_SENDER = '<EMAIL>'
```

### 5. SendGrid Configuration
```python
# SendGrid SMTP Settings
MAIL_SERVER = 'smtp.sendgrid.net'
MAIL_PORT = 587
MAIL_USE_TLS = True
MAIL_USE_SSL = False
MAIL_USERNAME = 'apikey'  # Always 'apikey' for SendGrid
MAIL_PASSWORD = 'your-sendgrid-api-key'
MAIL_DEFAULT_SENDER = '<EMAIL>'
SECURITY_EMAIL_SENDER = '<EMAIL>'
```

### 6. Mailgun Configuration
```python
# Mailgun SMTP Settings
MAIL_SERVER = 'smtp.mailgun.org'
MAIL_PORT = 587
MAIL_USE_TLS = True
MAIL_USE_SSL = False
MAIL_USERNAME = '<EMAIL>'
MAIL_PASSWORD = 'your-mailgun-password'
MAIL_DEFAULT_SENDER = '<EMAIL>'
SECURITY_EMAIL_SENDER = '<EMAIL>'
```

## Additional Email Settings

### Email Templates and Branding
```python
# Email template settings
SECURITY_EMAIL_SUBJECT_REGISTER = "Welcome to My Site - Please confirm your email"
SECURITY_EMAIL_SUBJECT_PASSWORD_RESET = "My Site - Password Reset Instructions"
SECURITY_EMAIL_SUBJECT_PASSWORD_CHANGE = "My Site - Password Changed"

# Email template customization
MAIL_ASCII_ATTACHMENTS = True
MAIL_MAX_EMAILS = None  # No limit on emails per connection
```

### Development/Testing Settings
```python
# For development - disable email sending
MAIL_SUPPRESS_SEND = True  # Set to False in production

# For debugging email issues
MAIL_DEBUG = True  # Set to False in production

# Test email settings (prints to console instead of sending)
TESTING = True  # Only for development
```

### Security Settings
```python
# Email security settings
SECURITY_CONFIRMABLE = True  # Require email confirmation
SECURITY_REGISTERABLE = True  # Allow user registration
SECURITY_RECOVERABLE = True  # Allow password recovery
SECURITY_CHANGEABLE = True  # Allow password changes
SECURITY_LOGIN_WITHOUT_CONFIRMATION = False  # Require email confirmation before login

# Email confirmation settings
SECURITY_CONFIRM_EMAIL_WITHIN = "5 days"  # Email confirmation validity
SECURITY_RESET_PASSWORD_WITHIN = "5 days"  # Password reset validity
```

## Configuration Steps

### Step 1: Edit Configuration File
```bash
# Navigate to your project directory
cd my-site

# Edit the configuration file
nano invenio.cfg
# or
vim invenio.cfg
```

### Step 2: Add SMTP Settings
Copy the appropriate configuration from above and paste it into your `invenio.cfg` file.

### Step 3: Restart Services
```bash
# Restart InvenioRDM containers to apply changes
invenio-cli containers restart
```

### Step 4: Test Email Configuration
```bash
# Test email sending from within the container
docker exec my-site-web-api-1 invenio shell -c "
from flask_mail import Mail, Message
from flask import current_app
mail = Mail(current_app)
msg = Message('Test Email', recipients=['<EMAIL>'])
msg.body = 'This is a test email from InvenioRDM'
try:
    mail.send(msg)
    print('Email sent successfully!')
except Exception as e:
    print(f'Email failed: {e}')
"
```

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Check username/password
   - For Gmail/Yahoo: Use App Passwords
   - Verify 2FA is enabled if required

2. **Connection Timeout**
   - Check MAIL_SERVER and MAIL_PORT
   - Verify firewall settings
   - Try different ports (587, 465, 25)

3. **TLS/SSL Errors**
   - Try different combinations of MAIL_USE_TLS and MAIL_USE_SSL
   - Port 587 usually uses TLS
   - Port 465 usually uses SSL

4. **Emails Not Sending**
   - Check MAIL_SUPPRESS_SEND is False
   - Verify email templates exist
   - Check application logs for errors

### Testing Commands
```bash
# Check email configuration
docker exec my-site-web-api-1 invenio shell -c "
from flask import current_app
print('MAIL_SERVER:', current_app.config.get('MAIL_SERVER'))
print('MAIL_PORT:', current_app.config.get('MAIL_PORT'))
print('MAIL_USE_TLS:', current_app.config.get('MAIL_USE_TLS'))
print('MAIL_USERNAME:', current_app.config.get('MAIL_USERNAME'))
"

# Send test registration email
docker exec my-site-web-api-1 invenio <NAME_EMAIL> --password=test123 --active
```

## Security Best Practices

1. **Use App Passwords** for Gmail/Yahoo instead of regular passwords
2. **Enable 2FA** on email accounts used for SMTP
3. **Use environment variables** for sensitive data in production:
   ```python
   import os
   MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
   MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
   ```
4. **Restrict SMTP access** to specific IP addresses if possible
5. **Monitor email usage** to detect abuse
6. **Use dedicated email accounts** for system notifications

## Production Recommendations

1. **Use dedicated SMTP service** (SendGrid, Mailgun, Amazon SES)
2. **Set up SPF, DKIM, and DMARC** records for your domain
3. **Monitor email delivery rates** and bounce rates
4. **Implement email rate limiting** to prevent abuse
5. **Use separate email addresses** for different types of notifications
6. **Set up email logging** for audit purposes

Remember to restart your InvenioRDM containers after making configuration changes!
