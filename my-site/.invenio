[cli]
flavour = RDM
logfile = /logs/invenio-cli.log

[cookiecutter]
project_name = My Site
project_shortname = my-site
project_site = my-site.com
github_repo = my-site/my-site
description = My Site InvenioRDM Instance
author_name = CERN
author_email = <EMAIL>
year = 2025
python_version = 3.9
database = postgresql
elasticsearch = 7
file_storage = local
development_tools = yes
_template = https://github.com/inveniosoftware/cookiecutter-invenio-rdm.git
_output_dir = /home/<USER>/workspaces/inveniordm
_repo_dir = /home/<USER>/.cookiecutters/cookiecutter-invenio-rdm
_checkout = v9.1

[files]
invenio.cfg = ea70ea0b3f02d61edbc041e91cf1118b8ac1cf2ae5dc2103f1a8cb64e627247f
app_data = {'vocabularies': {'affiliations_ror.yaml': '2e913fbecacd3694cba1b9a10739164911c2d45db3b32f1cb923027c48e0ac3e', 'subjects_oecd_fos.yaml': 'eef38714c506d956193c3be17e6004049525d252bf22424f4804dfc1ad46db44'}, 'vocabularies.yaml': '21218eeb7c8c08918e242d1ea8110328e37d7cd891ccc7ee142b6f4aba34ba08', 'README.md': 'd58978488626bdeddc791a15a39e341a4ed06f202b84d0c0d4aa9ef49481c6d9'}
dockerfile = f9b6dd1f450643016d5b4bf6261f6400ec75d208df4f8b348ce2b3ac2a29c5e3
docker-services.yml = 68370b444b0b4555b8700ff3b941529f51559d2a47e88cc55dbf4f724a0aed26
templates = {'.gitkeep': 'd4756cdf68d94c670c924f9d57d44718f666e457727701486435b57a97e52c5f'}
docker-compose.full.yml = fbd148a9541103ec1f7c26448c9e51f3697f0bc51cc7e5c10ee0daa37fae74a8
docker-compose.yml = 8b66658940c970dac0bd720e3af4419c6a3cb8b71ea183024f6e17835ba7a9aa
.gitignore = af1182c70588e583daeb35809e6cdfe3408425d438c3ceb282b1f4b8360290e2
static = {'images': {'invenio-rdm.svg': '50f09b4d83244a69c58f44aa436a3fa097726d9b47fd0e0782ea92d31f854b65', 'logo-invenio-white.svg': '5af2c7b67a09798cba3d50cc2e811ca9fd2ff73b40d9d2a52a97237b9ed44008'}}
.dockerignore = 21241bc12cdf4ac5f209d6b191adc8f25eb89af93ce5c4c3f72c86661162d7b5
readme.md = af9038dea06078d60fee30ba36932caeb18affdf873a318b921cd9978a073476
assets = {'templates': {'search': {'.gitkeep': 'd4756cdf68d94c670c924f9d57d44718f666e457727701486435b57a97e52c5f'}}, 'less': {'theme.config': 'ee68441b779400585a3a1ed4445ab5727e3a47a09aa708976dd506c0e081f0d8', 'site': {'globals': {'site.overrides': 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855', 'site.variables': 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855'}}}}
docker = {'pgadmin': {'servers.json': '047b4f334764ad0c1fa89b69c8d635dfc74a4af40e18845e532c2131f0b34e6a'}, 'nginx': {'conf.d': {'default.conf': '9397770d56124ae53848e300bdf976d55653c47fbf8d47a7dc8c3ee2b830e6d5'}, 'Dockerfile': '541326766beaba2e8492255c99e11aa76640d71329ceab5bcab1c8d6a979bd5c', 'test.key': 'd33fcbc267921d4d491086dfc2332a21b45718207f696ede3df134dd68066bd1', 'nginx.conf': '6c9a6e372fc340dfcd3b49e6a30bd7bd36af41d79d1d093b7b3ba5f4c9d781b4', 'test.crt': '277b4a34416ba157de8edba27c2429f50bdf2f82c8d6bd10cad6aa3a12f1dd8d'}, 'uwsgi': {'uwsgi_ui.ini': 'c80168dc8d29086b876caab41b5057fddef46bdc748c5ecf213f684aa73cc90c', 'uwsgi_rest.ini': 'c36fc5021baf15f375b3b6c825f7753e5d166319c5fe38a85b604391ce5b37b6'}}
pipfile = 4b85e546fc6cf8449acb10039c4c2196cb881e7f180fcb556aac5668a88837af

