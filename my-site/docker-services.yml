version: '2.2'
services:
  app:
    build:
      context: ./
      args:
        - ENVIRONMENT=DEV
    image: my-site
    restart: "unless-stopped"
    environment:
      - "INVENIO_ACCOUNTS_SESSION_REDIS_URL=redis://cache:6379/1"
      - "INVENIO_BROKER_URL=**************************/"
      - "INVENIO_CACHE_REDIS_URL=redis://cache:6379/0"
      - "INVENIO_CACHE_TYPE=redis"
      - "INVENIO_CELERY_BROKER_URL=**************************/"
      - "INVENIO_CELERY_RESULT_BACKEND=redis://cache:6379/2"
      - "INVENIO_SEARCH_ELASTIC_HOSTS=['es:9200']"
      - "INVENIO_SECRET_KEY=CHANGE_ME"
      - "INVENIO_SQLALCHEMY_DATABASE_URI=postgresql+psycopg2://my-site:my-site@db/my-site"
      - "INVENIO_WSGI_PROXIES=2"
      - "INVENIO_RATELIMIT_STORAGE_URL=redis://cache:6379/3"
  frontend:
    build: ./docker/nginx/
    image: my-site-frontend
    restart: "unless-stopped"
    ports:
      - "80"
      - "443"
  cache:
    image: redis
    restart: "unless-stopped"
    read_only: true
    ports:
      - "6379:6379"
  db:
    image: postgres:12.4
    restart: "unless-stopped"
    environment:
      - "POSTGRES_USER=my-site"
      - "POSTGRES_PASSWORD=my-site"
      - "POSTGRES_DB=my-site"
    ports:
      - "5432:5432"
  pgadmin:
    image: dpage/pgadmin4:5.2
    restart: "unless-stopped"
    ports:
      - "5050:80"
      - "5051:443"
    environment:
      PGADMIN_DEFAULT_EMAIL: "<EMAIL>"
      PGADMIN_DEFAULT_PASSWORD: "my-site"
    volumes:
      - ./docker/pgadmin/servers.json:/pgadmin4/servers.json
  mq:
    image: rabbitmq:3.8-management
    restart: "unless-stopped"
    ports:
      - "15672:15672"
      - "5672:5672"
  es:
    image: docker.elastic.co/elasticsearch/elasticsearch-oss:7.10.2
    restart: "unless-stopped"
    environment:
      - bootstrap.memory_lock=true
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - discovery.type=single-node
    healthcheck:
      test: ["CMD", "curl", "-f", "localhost:9200/_cluster/health?wait_for_status=green"]
      interval: 30s
      timeout: 30s
      retries: 5
    ulimits:
      memlock:
        soft: -1
        hard: -1
    mem_limit: 1g
    ports:
      - "9200:9200"
      - "9300:9300"
  kibana:
    image: docker.elastic.co/kibana/kibana-oss:7.10.2
    environment:
      - "ELASTICSEARCH_HOSTS=http://es:9200"
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "5601:5601"
